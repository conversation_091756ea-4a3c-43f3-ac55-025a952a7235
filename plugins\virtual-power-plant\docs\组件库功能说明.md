# 组件库功能说明文档

## 概述

本文档详细介绍了虚拟电厂项目中使用的两个核心组件库：`cet-common` 和 `eem-base/components` 的功能特性和使用场景。

---

## 📦 cet-common 组件库 (v1.7.4)

### 简介
`cet-common` 是一个企业级Vue组件库，基于ElementUI进行增强，提供了15个核心组件，专为数据密集型管理系统设计。

### 🔧 核心组件

#### 1. CetDialog - 对话框组件
**功能特性：**
- 基于ElementUI Dialog增强
- 支持拖拽功能 (`v-dialogDrag`)
- 触发器控制开关 (`openTrigger_in`, `closeTrigger_in`)
- 自定义宽度和样式
- 防止点击遮罩层关闭

**使用场景：**
- 新增/编辑数据弹窗
- 确认操作对话框
- 详情信息展示
- 复杂表单录入

**配置示例：**
```javascript
CetDialog_addUnit: {
  title: "新增",
  width: "960px",
  openTrigger_in: 0,
  closeTrigger_in: 0,
  showClose: true
}
```

#### 2. CetTable - 表格组件
**功能特性：**
- 高级数据表格，支持自动分页
- 多种数据模式：后端接口/组件/静态数据
- 支持排序、筛选、多选
- 键盘操作支持 (Shift多选)
- 列宽自动调整
- 数据导出功能

**使用场景：**
- 数据列表展示
- 数据管理界面
- 报表展示
- 批量操作界面

**配置示例：**
```javascript
CetTable_units: {
  data: [],
  queryMode: "component",
  dataMode: "component",
  showPagination: true,
  border: true
}
```

#### 3. CetForm - 表单组件
**功能特性：**
- 智能表单，支持数据绑定
- 多种数据获取模式
- 自动验证和提交
- 支持查询和写入操作
- 国际化支持

**使用场景：**
- 数据录入表单
- 查询条件表单
- 编辑信息表单
- 配置设置表单

#### 4. CetButton - 按钮组件
**功能特性：**
- 增强版按钮组件
- 状态控制和权限管理
- 触发器机制

**使用场景：**
- 操作按钮
- 状态切换
- 权限控制按钮

#### 5. CetDateSelect - 日期选择组件
**功能特性：**
- 日期时间选择器
- 支持季度选择 (`seasonPicker.vue`)
- 自定义日期范围

**使用场景：**
- 时间范围查询
- 日期数据录入
- 报表时间选择

#### 6. 其他专业组件

**CetTree - 树形组件**
- 层级数据展示
- 节点选择和操作

**CetGiantTree - 大数据量树组件**
- 处理大量树形数据
- 性能优化的树组件

**CetTabs - 标签页组件**
- 多页面内容切换
- 动态标签管理

**CetTransfer - 穿梭框组件**
- 数据转移选择
- 批量数据操作

**CetIcon - 图标组件**
- 统一图标管理
- 自定义图标支持

**CetAside - 侧边栏组件**
- 导航侧边栏
- 可折叠侧边栏

**CetInterface - 接口组件**
- API接口调用组件
- 数据接口管理

**CetSimpleSelect - 简单选择器**
- 轻量级下拉选择
- 快速选择组件

**CetZtree - zTree树组件**
- 基于zTree的树形组件
- 支持模糊搜索

### 🛠️ 工具功能

**Excel导出：**
```javascript
import { vendor } from 'cet-common';
const excel = await vendor.excel();
```

**API注册机制：**
- 统一API管理
- 自动接口绑定

**指令支持：**
- `v-dialogDrag` - 对话框拖拽
- `v-drag` - 元素拖拽

---

## 📦 eem-base/components 组件库

### 简介
`eem-base` 是专为能源管理系统设计的基础组件库，提供了8个专用组件，针对能源数据展示和操作进行了优化。

### 🔧 核心组件

#### 1. CustomElSelect - 自定义下拉选择器
**功能特性：**
- 带前缀标签的下拉选择器
- 支持帮助提示 (`popover_in`)
- 禁用状态样式
- 自定义边框和背景

**使用场景：**
- 筛选条件选择
- 分类数据选择
- 配置项选择

**使用示例：**
```vue
<CustomElSelect
  v-model="selectedType"
  :prefix_in="'机组类型'"
  :popover_in="'选择机组的类型'"
>
  <ElOption label="火电机组" value="thermal" />
</CustomElSelect>
```

#### 2. CustomElDatePicker - 自定义日期选择器
**功能特性：**
- 带前缀标签的日期选择器
- 统一样式设计
- 支持各种日期类型

**使用场景：**
- 时间查询条件
- 日期数据录入
- 时间范围选择

#### 3. TimeRange - 时间范围组件
**功能特性：**
- 时间范围选择器
- 前后导航按钮
- 快捷时间选择：
  - 今天/昨天
  - 本周/上周
  - 本月/上月
  - 本年/去年
- 自定义时间范围

**使用场景：**
- 数据查询的时间范围
- 报表时间选择
- 历史数据查看

#### 4. UploadDialog - 上传对话框
**功能特性：**
- 文件拖拽上传
- 文件类型限制
- 文件大小限制
- 模板下载功能
- 上传进度显示

**使用场景：**
- 批量数据导入
- Excel文件上传
- 配置文件上传
- 模板文件下载

#### 5. TreeTransfer - 树形穿梭框
**功能特性：**
- 树形结构数据转移
- 支持父子节点关联
- 批量选择和转移

**使用场景：**
- 权限分配
- 组织架构选择
- 层级数据关联

#### 6. 其他专用组件

**chartTypeSwitch - 图表类型切换**
- 图表展示类型切换
- 数据可视化控制

**customSteps - 自定义步骤条**
- 流程步骤展示
- 进度指示器

**legendInteraction - 图例交互**
- 图表图例交互
- 数据系列控制

**textKeyword - 文本关键字**
- 关键字高亮显示
- 文本搜索匹配

---

## 🎯 使用场景总结

### cet-common 适用场景：
- ✅ 企业级管理系统
- ✅ 数据密集型应用
- ✅ 复杂表格和表单场景
- ✅ 多语言支持系统
- ✅ 权限管理系统

### eem-base 适用场景：
- ✅ 能源管理系统
- ✅ 时间序列数据查询
- ✅ 数据导入导出功能
- ✅ 图表数据展示
- ✅ 层级数据管理

---

## 📋 最佳实践

### 1. 组件组合使用
```vue
<!-- 典型的查询表单 + 数据表格组合 -->
<CetForm>
  <CustomElSelect :prefix_in="'类型'" />
  <TimeRange v-model="timeRange" />
</CetForm>

<CetTable :data="tableData">
  <!-- 表格列定义 -->
</CetTable>
```

### 2. 弹窗表单模式
```vue
<CetDialog v-bind="dialogConfig">
  <CetForm :data="formData">
    <!-- 表单内容 -->
  </CetForm>
</CetDialog>
```

### 3. 数据上传流程
```vue
<UploadDialog
  :dialogTitle="'批量导入'"
  :extensionName="'xlsx,xls'"
  @upload-success="handleUploadSuccess"
/>
```

---

## 🔧 配置说明

### 全局配置
```javascript
// main.js
Vue.use(CetCommon, {
  api: customApi,
  CetDialog: {
    isDraggable: false
  },
  CetTable: {
    isColumsHeaderDoLayout: true
  }
});
```

### 组件引入
```javascript
import { CustomElSelect, CustomElDatePicker } from "eem-base/components";
Vue.component("CustomElSelect", CustomElSelect);
Vue.component("CustomElDatePicker", CustomElDatePicker);
```

---

## 📝 注意事项

1. **版本兼容性**：确保Vue 2.x版本兼容
2. **样式依赖**：需要ElementUI和相关CSS变量
3. **API配置**：cet-common需要配置API接口
4. **国际化**：支持多语言，需要配置语言包
5. **权限控制**：部分组件支持权限指令

---

*最后更新时间：2025-01-08*

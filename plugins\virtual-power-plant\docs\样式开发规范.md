# @omega/theme 样式开发规范

## 1. 概述

本规范基于 `@omega/theme` 主题库，旨在统一项目中的样式开发标准，确保代码的一致性、可维护性和主题切换的兼容性。

## 2. 主题系统

### 2.1 支持的主题
- `light`: 浅色主题（默认）
- `dark`: 深色主题
- `blue`: 蓝色主题
- `bluex`: 蓝色扩展主题

### 2.2 主题切换
通过设置 HTML 根元素的 `data-theme` 属性来切换主题：
```html
<html data-theme="dark">
```

## 3. 变量使用规范

### 3.1 间距变量（必须使用）
```scss
// ✅ 正确使用
@include padding(J1, J2);        // 8px 12px
@include margin_top(J3);         // 16px
.my-class { gap: mh-get(J4); }   // 24px

// ❌ 错误使用
padding: 8px 12px;               // 硬编码数值
margin-top: 16px;                // 硬编码数值
```

**间距变量对照表：**
- `J0`: 4px（最小间距）
- `J1`: 8px（小间距）
- `J2`: 12px（中小间距）
- `J3`: 16px（中间距）
- `J4`: 24px（大间距）
- `J5`: 32px（最大间距）

### 3.2 字体大小变量（必须使用）
```scss
// ✅ 正确使用
@include font_size(Aa);          // 14px 正文
@include font_size(H3);          // 16px 三级标题

// ❌ 错误使用
font-size: 14px;                 // 硬编码数值
```

**字体大小对照表：**
- `H`: 28px（主标题）
- `H1`: 22px（一级标题）
- `H2`: 18px（二级标题）
- `H3`: 16px（三级标题）
- `Aa`: 14px（正文）
- `Ab`: 12px（小字体）

### 3.3 颜色变量（强制使用）
```scss
// ✅ 正确使用
@include background_color(ZS);   // 主色背景
@include font_color(T1);         // 主要文字色
@include border_color(B1);       // 主要边框色

// ❌ 错误使用
background-color: #00b45e;       // 硬编码颜色
color: #13171f;                  // 硬编码颜色
```

## 4. 颜色系统规范

### 4.1 品牌色
- `ZS`: 主色（用于主要按钮、链接等）
- `F1`: 辅助色1（用于次要操作）
- `F2`: 辅助色2（用于警示操作）

### 4.2 状态色
- `Sta1`: 成功色（绿色系）
- `Sta2`: 警告色（橙色系）
- `Sta3`: 危险色（红色系）
- `Sta4`: 一般色（灰色系）
- `Sta5`: 次要色（蓝色系）

### 4.3 文字色层级
- `T1`: 主要文字（最重要的文字内容）
- `T2`: 常规文字（正文内容）
- `T3`: 次要文字（辅助说明文字）
- `T4`: 占位文字（placeholder、禁用状态）
- `T5`: 带背景的文字（白色文字）
- `T6`: 禁用文字（不可交互状态）

### 4.4 背景色层级
- `BG`: 页面背景色
- `BG1`: 主要背景色（卡片、面板）
- `BG2`: 滑入背景色（hover状态）
- `BG3`: 点击背景色（active状态）
- `BG4`: 选中背景色（selected状态）
- `BG5`: 次亮背景色
- `BG6`: 高亮背景色

## 5. SCSS 混合器使用规范

### 5.1 颜色混合器
```scss
// 背景色
@include background_color(ZS);
@include background_color(BG1, !important);

// 文字色
@include font_color(T1);
@include font_color(T2, !important);

// 边框色
@include border_color(B1);
@include border_direction_color(B1, top);

// SVG 填充色
@include fill_color(ZS);
```

### 5.2 尺寸混合器
```scss
// 字体大小
@include font_size(Aa);
@include line_height(H3);

// 内边距
@include padding(J1);              // 单值
@include padding(J1, J2);          // 双值
@include padding_left(J3);         // 单方向

// 外边距
@include margin(J2, J3);
@include margin_top(J1);
```

### 5.3 交互效果混合器
```scss
// 悬停效果（自动处理hover和active状态）
.button {
  @include hover;
}
```

## 6. 通用样式类使用规范

### 6.1 布局类
```scss
.fullfilled    // 100% 宽高
.fullwidth     // 100% 宽度
.fullheight    // 100% 高度
.middle-flex   // 垂直水平居中
.text-center   // 文字居中
.clearfix      // 清除浮动
```

### 6.2 快捷间距类（谨慎使用）
```scss
// 仅在快速原型或特殊情况下使用
.m8, .m16, .m24    // margin
.p8, .p16, .p24    // padding
.mt8, .mr16        // 方向性间距
```

### 6.3 响应式字体类（谨慎使用）
```scss
.fs14, .fs16, .fs18  // 字体大小类
```

## 7. 开发最佳实践

### 7.1 强制规范
1. **禁止硬编码颜色值**：必须使用主题变量
2. **禁止硬编码尺寸值**：优先使用预定义变量
3. **必须考虑主题兼容性**：确保在所有主题下正常显示
4. **必须使用语义化变量名**：选择最符合用途的变量

### 7.2 推荐实践
1. **优先使用混合器**：而不是直接使用CSS属性
2. **保持一致性**：同类元素使用相同的变量
3. **分层设计**：合理使用文字色和背景色的层级关系
4. **测试多主题**：开发完成后测试所有主题效果

### 7.3 代码示例
```scss
// ✅ 推荐写法
.my-component {
  @include background_color(BG1);
  @include font_color(T2);
  @include border_color(B1);
  @include padding(J2, J3);
  @include font_size(Aa);
  
  &:hover {
    @include background_color(BG2);
  }
  
  .title {
    @include font_color(T1);
    @include font_size(H3);
    @include margin_bottom(J2);
  }
}

// ❌ 不推荐写法
.my-component {
  background-color: #ffffff;
  color: #424e5f;
  border: 1px solid #dcdfe6;
  padding: 12px 16px;
  font-size: 14px;
}
```

## 8. 主题扩展

### 8.1 自定义主题
如需添加新主题，需要：
1. 在 `tailwind.var.css` 中添加新的主题变量定义
2. 在 `theme.js` 中注册新主题
3. 创建对应的 ElementUI 主题文件

### 8.2 变量扩展
如需添加新变量，需要：
1. 在 `_var.scss` 中定义 SCSS 变量
2. 在 `tailwind.var.css` 中定义 CSS 变量
3. 在 `_handle.scss` 中添加对应的混合器

## 9. 常见问题

### 9.1 主题切换不生效
- 检查是否正确设置了 `data-theme` 属性
- 确认使用了主题变量而不是硬编码值

### 9.2 颜色在某个主题下显示异常
- 检查是否使用了正确的语义化变量
- 确认变量在所有主题中都有定义

### 9.3 间距不统一
- 统一使用预定义的间距变量
- 避免使用快捷类和硬编码值的混合

---

**注意：本规范为强制执行标准，所有样式开发必须严格遵循。**

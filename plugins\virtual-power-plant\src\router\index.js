/** @type {import("vue-router").RouteConfig[]} */

import main from "./main.vue";

export const appRoutes = [
  {
    path: "/",
    component: main,
    children: [
      {
        meta: {
          keepAlive: true
        },
        path: "/aggregation",
        component: () => import("@/projects/vpp-resource-manager/aggregation/index.vue")
      },

    ]
  }
];

export default {
  // mode: "history",
  routes: appRoutes
};

# 前端开发规范

## 页面开发前，优先查阅/复用已有样式文件和变量，避免重复造轮子。
## 重要：优先使用omega和cet

## 1. 目录结构与命名规范

- 每个插件/模块建议采用如下结构：
  ```
  src/
    components/      # 通用/业务组件
    utils/           # 工具函数
    api/             # 接口封装
    store/           # 状态管理
    projects/        # 业务模块
    router/          # 路由
    resources/       # 静态资源
    icons/           # 图标
    config/          # 配置
    hooks/           # 通用hooks（如有）
  ```
- 目录、文件命名统一采用小写+中划线风格（如 multi-select.vue）。
- 组件目录命名统一为 components。

---



## 2. API 封装与状态管理

- **API**：所有接口请求集中于`src/api/`，每个业务模块单独文件，统一封装。
- **状态管理**：所有store模块集中于`src/store/`，按业务拆分子模块。

---

## 3. 资源与主题管理

- 图片、图标等静态资源统一放于`resources/`或`assets/`，按业务/主题（light/dark）细分。
- 主题切换相关资源需有统一命名和引用方式。

---

## 4. 代码风格与提交规范

- 使用ESLint+Prettier自动格式化，配置文件放于根目录。
- 推荐使用Husky+lint-staged进行提交前代码检查。
- Git分支管理建议采用feature/bugfix/release等前缀，提交信息遵循Conventional Commits规范。

---

## 5. projects目录下业务插件/模块开发规范

- 每个业务模块（如projects/unitCostAnalysis、projects/topologyConfig等）应包含：
  - 业务主入口文件（如index.vue）
  - 业务相关子组件（如components/目录）
  - 业务相关静态资源（如assets/目录）
  - 业务相关配置（如columnConfig.js等）
- 业务模块内部结构建议如下：
  ```
  projects/
    <业务模块名>/
      index.vue           # 业务主入口
      components/         # 业务相关组件
      assets/             # 业务相关资源
      config/             # 业务相关配置
      ...                 # 其他业务相关文件
  ```
- 业务模块命名采用小写+中划线风格，组件命名与功能保持一致。
- 业务模块内如有通用组件，优先放在本模块components目录下。

---
## 6.其他
充分利用 Omega 组件和工具，减少重复造轮子。
组件、工具函数、API 封装等需写注释和必要文档。
复杂逻辑建议拆分为 hooks 或 utils，保持组件简洁。
遵循团队协作和代码评审流程。

## 7.cet 系列组件说明
组件放在node_modules目录下，不清楚可以读取该目录下的文件查找具体组件使用方法
### cet-chart
- **功能**：基于 ECharts 的图表组件，支持多主题（dark、light、blue、bluex），可通过 `options`、`initOptions`、`autoresize` 等属性灵活配置图表，支持数据响应式、主题切换、国际化、地图注册等。
- **典型用法**：
  ```vue
  <CetChart :options="chartOptions" :inputData_in="dataSource" />
  ```
- **适用场景**：需要可定制化、响应式的图表展示。

### cet-common
- **功能**：常用业务组件集合，统一注册，便于快速开发。包含但不限于：
  - `CetButton`：按钮组件，支持显示/禁用、点击事件，常用于表单、操作区。
  - `CetTable`：表格组件，支持分页、树形结构、导出、数据联动、批量操作等，适合复杂数据展示与交互。
  - `CetDialog`：弹窗组件，支持拖拽、标题、footer插槽、开关控制，适合弹出表单、提示、确认等场景。
  - `CetForm`：表单组件，支持数据绑定、校验、后端/本地数据模式，适合复杂表单录入与校验。
  - `CetTree`：树组件，支持节点选择、过滤、懒加载，适合层级数据展示与选择。
  - `CetTabs`：基于 Element UI Tabs 的高阶组件，支持 keep-alive，内容切换时可触发生命周期，适合多页面/多视图切换。
  - `CetAside`：侧边栏布局组件，支持折叠/展开，常用于页面主内容与侧边栏分区。
  - `CetZtree`：基于 zTree 的树组件，支持搜索、节点多选、懒加载、节点操作等，适合大数据量树形结构。
  - `CetTransfer`：树型穿梭框组件，支持树节点选择与右侧列表管理，适合复杂权限、组织结构等多选场景。
  - `CetIcon`：SVG 图标组件，支持自定义 iconClass，适合统一管理和使用自定义图标。
  - `CetGiantTree`：大数据量树组件，支持异步加载、节点多选、搜索、层级自定义，适合超大树结构场景。
  - `CetSimpleSelect`：下拉选择组件，支持动态数据、接口联动，适合表单、筛选等场景。
  - `CetDateSelect`：日期选择组件，支持日/周/月/季/年/区间等多种时间粒度，适合报表、数据筛选等。
  - `CetInterface`：接口数据组件，支持灵活配置数据源、查询参数、动态输入，适合与表单、表格等组件联动。
- **典型用法**：
  ```js
  import CetCommon from 'cet-common';
  Vue.use(CetCommon);
  // 组件全局可用
  ```
- **适用场景**：表单、弹窗、表格、树、按钮等常见业务 UI 场景。

### cet-graph
- **功能**：复杂图形组态组件，支持 PecDraw 组态画面在 Web 端的还原与交互，支持多种数据源（PecStar、Matterhorn）、远程控制、动画、视频嵌入、HLS/FLV 直播、云台控制等。
- **典型用法**：
  ```vue
  <CetGraph v-bind="CetGraphAttr" />
  ```
  其中 `CetGraphAttr` 支持如 `path_in`（图形路径）、`userName_in`（用户）、`refresh_trigger_in`（刷新）、`associatedDevices_in`（设备关联）等参数。
- **适用场景**：工业组态、监控大屏、复杂图形交互、远程控制等。

---
description: 页面样式生成与维护规范，确保风格统一、易维护、便于与设计稿一致
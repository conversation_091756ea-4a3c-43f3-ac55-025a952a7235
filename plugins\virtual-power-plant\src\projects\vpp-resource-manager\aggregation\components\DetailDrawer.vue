<template>
  <ElDrawer
    class="detail-drawer"
    :title="$T('详情')"
    :visible.sync="openDrawer"
    destroy-on-close
    size="960px"
  >
    <div class="drawer-content">
      <!-- 基础信息 -->
      <div class="basic-info-section">
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="info-item">
              <div class="label">机组名称</div>
              <div class="value">{{ detailData.unitName || "--" }}</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <div class="label">机组类型</div>
              <div class="value">{{ detailData.unitTypeName || "--" }}</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 资源列表 -->
      <div class="resource-list-section">
        <div class="section-title">调峰机组资源列表</div>

        <!-- 资源表格 -->
        <div class="resource-table">
          <CetTable
            :data.sync="CetTable_resources.data"
            v-bind="CetTable_resources"
            v-on="CetTable_resources.event"
            :border="false"
          >
            <!-- 序号列 -->
            <ElTableColumn v-bind="ElTableColumn_index" />

            <!-- 资源ID列 -->
            <ElTableColumn v-bind="ElTableColumn_resourceId" />

            <!-- 资源名称列 -->
            <ElTableColumn v-bind="ElTableColumn_resourceName" />

            <!-- 区域列 -->
            <ElTableColumn v-bind="ElTableColumn_area" />

            <!-- 地址列 -->
            <ElTableColumn v-bind="ElTableColumn_address" />

            <!-- 操作列 -->
            <ElTableColumn v-bind="ElTableColumn_operations">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  @click="handleUnbind(scope.row)"
                  class="unbind-text"
                >
                  解绑
                </el-button>
              </template>
            </ElTableColumn>
          </CetTable>
        </div>

        <!-- 分页器 -->
        <div class="pagination-wrapper">
          <div class="pagination-total">
            共
            <span class="total-number">{{ total }}</span>
            个
          </div>
          <el-pagination
            class="pagination-container"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 15, 20, 50, 100]"
            :page-size="pageSize"
            :total="total"
            layout="sizes, prev, pager, next, jumper"
          />
        </div>
      </div>
    </div>
  </ElDrawer>
</template>

<script>
export default {
  name: "DetailDrawer",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number,
      default: 0
    },
    closeTrigger_in: {
      type: Number,
      default: 0
    },
    inputData_in: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      openDrawer: false,
      detailData: {},

      // 分页相关数据
      allResourcesData: [], // 存储所有资源数据
      currentPage: 1,
      pageSize: 10,
      total: 0,

      // 资源表格配置
      CetTable_resources: {
        data: [],
        queryMode: "trigger",
        dataMode: "component",
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "resources",
          dataIndex: [],
          modelList: [],
          filters: [],
          hasQueryNode: false
        },
        queryTrigger_in: new Date().getTime(),
        showPagination: false,
        event: {}
      },

      // 表格列配置
      ElTableColumn_index: {
        type: "index",
        label: "序号",
        headerAlign: "center",
        align: "center",
        width: "70"
      },

      ElTableColumn_resourceId: {
        prop: "resourceId",
        label: "资源ID",
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "180"
      },

      ElTableColumn_resourceName: {
        prop: "resourceName",
        label: "资源名称",
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "200"
      },

      ElTableColumn_area: {
        prop: "area",
        label: "区域",
        headerAlign: "center",
        align: "center",
        showOverflowTooltip: true,
        width: "120",
        formatter: row => {
          return row.area || "--";
        }
      },

      ElTableColumn_address: {
        prop: "address",
        label: "地址",
        headerAlign: "left",
        align: "left",
        showOverflowTooltip: true,
        minWidth: "200",
        formatter: row => {
          return row.address || "--";
        }
      },

      ElTableColumn_operations: {
        label: "操作",
        headerAlign: "center",
        align: "center",
        width: "100",
        fixed: "right"
      }
    };
  },

  watch: {
    visibleTrigger_in() {
      this.openDrawer = true;
      // 当抽屉打开时，确保数据已生成
      if (this.detailData && Object.keys(this.detailData).length > 0) {
        this.generateResourceData();
      }
    },
    closeTrigger_in() {
      this.openDrawer = false;
    },
    inputData_in(val) {
      this.detailData = val || {};
      if (this.openDrawer) {
        this.generateResourceData();
      }
    }
  },

  methods: {
    // 生成模拟资源数据
    generateResourceData() {
      const resourceCount = this.detailData.resourceCount || 0;
      const data = [];

      for (let i = 1; i <= resourceCount; i++) {
        data.push({
          id: i,
          resourceId: `9144030078525478X${i.toString().padStart(2, "0")}`,
          resourceName: "地铁物业管理发展有限公司",
          area:
            Math.random() > 0.5
              ? ""
              : ["江苏", "浙江", "上海", "安徽"][Math.floor(Math.random() * 4)],
          address: Math.random() > 0.5 ? "" : `地址${i}`
        });
      }

      this.allResourcesData = data;
      this.total = data.length;
      this.currentPage = 1;
      this.updateTableData();

      console.log("Generated resource data:", data.length, "items");
    },

    // 更新表格数据（分页处理）
    updateTableData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      this.CetTable_resources.data = this.allResourcesData.slice(start, end);
      // 触发表格更新
      this.$nextTick(() => {
        this.CetTable_resources.queryTrigger_in = new Date().getTime();
      });
    },

    // 分页大小变化处理
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.currentPage = 1;
      this.updateTableData();
    },

    // 当前页变化处理
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.updateTableData();
    },

    // 解绑处理
    handleUnbind(row) {
      this.$confirm("此操作将解绑该条数据，是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          // 从数据中移除该项
          const index = this.allResourcesData.findIndex(
            item => item.id === row.id
          );
          if (index > -1) {
            this.allResourcesData.splice(index, 1);
            this.total = this.allResourcesData.length;

            // 如果当前页没有数据了，回到上一页
            if (
              this.CetTable_resources.data.length === 1 &&
              this.currentPage > 1
            ) {
              this.currentPage--;
            }

            this.updateTableData();
            this.$message.success("解绑成功");
          }
        })
        .catch(() => {
          this.$message.info("已取消解绑");
        });
    }
  }
};
</script>

<style scoped>
.drawer-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 24px;
  overflow: hidden;
}

.basic-info-section {
  margin-bottom: 24px;
}

.section-title {
  font-weight: 500;
  font-size: 16px;
  color: var(--T1);
  margin-bottom: 16px;
}

.info-item {
  margin-bottom: 16px;
}

.label {
  font-size: 14px;
  color: var(--T4);
  margin-bottom: 4px;
}

.value {
  font-size: 14px;
  color: var(--T1);
}

.pagination-wrapper {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
}

.pagination-total {
  color: var(--T2);
  font-size: 14px;
}

.pagination-total .total-number {
  color: var(--ZS);
  font-weight: 500;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
}

.unbind-text {
  color: var(--ZS);
}

.unbind-text:hover {
  color: var(--ZS);
}
</style>
